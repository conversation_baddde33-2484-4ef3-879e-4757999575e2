"""
配置管理器
处理论文模式和扩展模式的配置切换，确保参数设置的一致性
"""

import logging
from typing import Dict, Any
import copy

logger = logging.getLogger(__name__)


class ConfigManager:
    """配置管理器，处理不同模式的配置切换"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化配置管理器
        
        Args:
            config: 原始配置字典
        """
        self.original_config = copy.deepcopy(config)
        self.config = config
        
    def apply_training_mode(self) -> Dict[str, Any]:
        """
        根据配置中的mode设置应用相应的训练参数
        
        Returns:
            更新后的配置字典
        """
        training_config = self.config.get('training', {})
        diffusion_config = training_config.get('diffusion', {})
        
        mode = diffusion_config.get('mode', 'extended')
        
        if mode == 'paper':
            self._apply_paper_mode()
            logger.info("应用论文模式配置")
        elif mode == 'extended':
            self._apply_extended_mode()
            logger.info("应用扩展模式配置")
        else:
            logger.warning(f"未知的训练模式: {mode}，使用扩展模式")
            self._apply_extended_mode()
            
        return self.config
    
    def _apply_paper_mode(self):
        """应用论文模式配置（严格按照论文Table V）"""
        paper_config = self.config['training']['diffusion']['paper_mode']
        
        # 更新训练参数
        self.config['training']['diffusion'].update({
            'epochs': paper_config['epochs'],
            'batch_size': paper_config['batch_size'],
            'learning_rate': paper_config['learning_rate'],
            'weight_decay': paper_config['weight_decay']
        })
        
        # 确保CDDPM参数与论文一致
        self.config['augmentation']['cddpm'].update({
            'timesteps': 1000,
            'beta_schedule': 'linear',
            'beta_start': 0.0001,
            'beta_end': 0.02
        })
        
        # 确保模型结构参数
        self.config['models']['diffusion'].update({
            'use_skip_connections': True,
            'dropout': 0.1
        })
        
        logger.info("论文模式配置:")
        logger.info(f"  训练轮数: {paper_config['epochs']}")
        logger.info(f"  批次大小: {paper_config['batch_size']}")
        logger.info(f"  学习率: {paper_config['learning_rate']}")
        logger.info(f"  权重衰减: {paper_config['weight_decay']}")
    
    def _apply_extended_mode(self):
        """应用扩展模式配置"""
        extended_config = self.config['training']['diffusion']['extended_mode']
        
        # 更新训练参数
        self.config['training']['diffusion'].update({
            'epochs': extended_config['epochs'],
            'batch_size': extended_config['batch_size'],
            'learning_rate': extended_config['learning_rate'],
            'weight_decay': extended_config['weight_decay']
        })
        
        logger.info("扩展模式配置:")
        logger.info(f"  训练轮数: {extended_config['epochs']}")
        logger.info(f"  批次大小: {extended_config['batch_size']}")
        logger.info(f"  学习率: {extended_config['learning_rate']}")
        logger.info(f"  权重衰减: {extended_config['weight_decay']}")
    
    def validate_config(self) -> bool:
        """
        验证配置的合理性
        
        Returns:
            配置是否有效
        """
        try:
            # 验证基本结构
            required_sections = ['dataset', 'augmentation', 'models', 'training']
            for section in required_sections:
                if section not in self.config:
                    logger.error(f"缺少必需的配置节: {section}")
                    return False
            
            # 验证扩散模型配置
            diffusion_config = self.config['models']['diffusion']
            if diffusion_config.get('hidden_dim', 0) <= 0:
                logger.error("hidden_dim必须大于0")
                return False
            
            # 验证训练配置
            training_config = self.config['training']['diffusion']
            if training_config.get('epochs', 0) <= 0:
                logger.error("epochs必须大于0")
                return False
            
            if training_config.get('learning_rate', 0) <= 0:
                logger.error("learning_rate必须大于0")
                return False
            
            # 验证CDDPM配置
            cddpm_config = self.config['augmentation']['cddpm']
            if cddpm_config.get('timesteps', 0) <= 0:
                logger.error("timesteps必须大于0")
                return False
            
            logger.info("配置验证通过")
            return True
            
        except Exception as e:
            logger.error(f"配置验证失败: {e}")
            return False
    
    def get_model_summary(self) -> Dict[str, Any]:
        """
        获取模型配置摘要
        
        Returns:
            模型配置摘要
        """
        diffusion_config = self.config['models']['diffusion']
        training_config = self.config['training']['diffusion']
        cddpm_config = self.config['augmentation']['cddpm']
        
        summary = {
            'model_type': 'CDDPM',
            'training_mode': training_config.get('mode', 'extended'),
            'model_params': {
                'hidden_dim': diffusion_config.get('hidden_dim', 128),
                'num_layers': diffusion_config.get('num_layers', 4),
                'num_heads': diffusion_config.get('num_heads', 8),
                'dropout': diffusion_config.get('dropout', 0.1),
                'use_skip_connections': diffusion_config.get('use_skip_connections', True)
            },
            'training_params': {
                'epochs': training_config.get('epochs', 10000),
                'batch_size': training_config.get('batch_size', 64),
                'learning_rate': training_config.get('learning_rate', 0.00001),
                'weight_decay': training_config.get('weight_decay', 0.0001)
            },
            'diffusion_params': {
                'timesteps': cddpm_config.get('timesteps', 1000),
                'beta_schedule': cddpm_config.get('beta_schedule', 'linear'),
                'unconditional_prob': cddpm_config.get('unconditional_prob', 0.1),
                'guidance_scale': cddpm_config.get('guidance_scale', 1.0)
            }
        }
        
        return summary
    
    def print_config_comparison(self):
        """打印论文模式和扩展模式的配置对比"""
        paper_config = self.config['training']['diffusion']['paper_mode']
        extended_config = self.config['training']['diffusion']['extended_mode']
        
        print("\n" + "="*60)
        print("配置模式对比")
        print("="*60)
        print(f"{'参数':<20} {'论文模式':<15} {'扩展模式':<15}")
        print("-"*60)
        print(f"{'训练轮数':<20} {paper_config['epochs']:<15} {extended_config['epochs']:<15}")
        print(f"{'批次大小':<20} {paper_config['batch_size']:<15} {extended_config['batch_size']:<15}")
        print(f"{'学习率':<20} {paper_config['learning_rate']:<15} {extended_config['learning_rate']:<15}")
        print(f"{'权重衰减':<20} {paper_config['weight_decay']:<15} {extended_config['weight_decay']:<15}")
        print(f"{'优化器':<20} {paper_config['optimizer']:<15} {extended_config['optimizer']:<15}")
        print(f"{'损失函数':<20} {paper_config['loss_function']:<15} {extended_config['loss_function']:<15}")
        print("="*60)
        
        current_mode = self.config['training']['diffusion'].get('mode', 'extended')
        print(f"当前使用模式: {current_mode}")
        print("="*60 + "\n")


def create_config_manager(config: Dict[str, Any]) -> ConfigManager:
    """
    创建配置管理器的工厂函数
    
    Args:
        config: 配置字典
        
    Returns:
        配置管理器实例
    """
    manager = ConfigManager(config)
    
    # 应用训练模式
    manager.apply_training_mode()
    
    # 验证配置
    if not manager.validate_config():
        logger.error("配置验证失败，请检查配置文件")
        raise ValueError("配置验证失败")
    
    # 打印配置摘要
    summary = manager.get_model_summary()
    logger.info(f"模型配置摘要: {summary}")
    
    return manager
