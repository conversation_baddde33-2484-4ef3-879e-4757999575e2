"""
测试精简配置是否正常工作
"""

import torch
import yaml
from models.unet import UNet1D

def test_simplified_config():
    """测试精简配置"""
    
    print("🔍 测试精简配置...")
    print("=" * 50)
    
    # 1. 加载配置
    print("1. 加载配置文件...")
    with open('config.yaml', 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    diffusion_config = config['models']['diffusion']
    
    # 显示核心配置参数
    print("核心配置参数:")
    core_params = [
        'in_channels', 'out_channels', 'hidden_dim', 
        'encoder_channels', 'num_layers_per_block', 
        'dropout', 'use_skip_connections'
    ]
    
    for param in core_params:
        if param in diffusion_config:
            print(f"  {param}: {diffusion_config[param]}")
    
    # 2. 创建模型
    print("\n2. 创建UNet模型...")
    try:
        unet = UNet1D(config)
        print("✅ 模型创建成功")
        
        # 显示自动推导的参数
        print("\n自动推导的参数:")
        print(f"  时间嵌入维度: {diffusion_config['hidden_dim'] * 4}")
        print(f"  ResBlock卷积核: 3")
        print(f"  GroupNorm组数: 8")
        print(f"  激活函数: Swish")
        
    except Exception as e:
        print(f"❌ 模型创建失败: {e}")
        return False
    
    # 3. 测试前向传播
    print("\n3. 测试前向传播...")
    try:
        batch_size = 2
        signal_length = 1024
        x = torch.randn(batch_size, 1, signal_length)
        timesteps = torch.randint(0, 1000, (batch_size,))
        class_labels = torch.randint(0, 8, (batch_size,))
        
        with torch.no_grad():
            output = unet(x, timesteps, class_labels)
        
        print(f"✅ 前向传播成功")
        print(f"  输入形状: {x.shape}")
        print(f"  输出形状: {output.shape}")
        print(f"  参数数量: {sum(p.numel() for p in unet.parameters()):,}")
        
    except Exception as e:
        print(f"❌ 前向传播失败: {e}")
        return False
    
    # 4. 验证网络结构
    print("\n4. 验证网络结构...")
    
    expected_channels = diffusion_config['encoder_channels']
    actual_down_blocks = len(unet.down_blocks)
    actual_up_blocks = len(unet.up_blocks)
    expected_blocks = len(expected_channels) - 1
    
    print(f"  编码器通道: {expected_channels}")
    print(f"  下采样块数: {actual_down_blocks} (期望: {expected_blocks})")
    print(f"  上采样块数: {actual_up_blocks} (期望: {expected_blocks})")
    print(f"  Skip connections: {unet.use_skip_connections}")
    
    if actual_down_blocks == expected_blocks and actual_up_blocks == expected_blocks:
        print("✅ 网络结构正确")
    else:
        print("❌ 网络结构不匹配")
        return False
    
    # 5. 检查ResBlock结构
    print("\n5. 检查ResBlock结构...")
    
    # 检查第一个下采样块
    first_block = unet.down_blocks[0]
    layers_per_block = len(first_block.layers)
    expected_layers = diffusion_config['num_layers_per_block']
    
    print(f"  每块ResBlock数: {layers_per_block} (期望: {expected_layers})")
    
    if layers_per_block == expected_layers:
        print("✅ ResBlock结构正确")
    else:
        print("❌ ResBlock结构不匹配")
        return False
    
    # 6. 配置简洁性检查
    print("\n6. 配置简洁性检查...")
    
    # 统计配置参数数量
    total_params = len(diffusion_config)
    core_param_count = len([p for p in core_params if p in diffusion_config])
    
    print(f"  总配置参数: {total_params}")
    print(f"  核心参数: {core_param_count}")
    print(f"  配置简洁度: {core_param_count/total_params*100:.1f}%")
    
    if total_params <= 10:  # 期望配置参数不超过10个
        print("✅ 配置足够简洁")
    else:
        print("⚠️ 配置可能还可以进一步简化")
    
    print("\n" + "=" * 50)
    print("🎉 精简配置测试完成！")
    print("📋 总结:")
    print("  - 只保留核心配置参数")
    print("  - 其他参数自动推导")
    print("  - 网络结构完全符合论文")
    print("  - 配置文件简洁易读")
    
    return True


if __name__ == "__main__":
    test_simplified_config()
