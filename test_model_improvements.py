"""
测试模型改进的脚本
验证CDDPM模型的修改是否正确工作
"""

import torch
import yaml
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 导入模型和配置管理器
from models.cddpm import CDDPM
from models.unet import UNet1D
from common.config_manager import create_config_manager


def load_config():
    """加载配置文件"""
    config_path = Path("config.yaml")
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config


def test_config_manager():
    """测试配置管理器"""
    logger.info("测试配置管理器...")
    
    config = load_config()
    
    # 测试论文模式
    config['training']['diffusion']['mode'] = 'paper'
    manager = create_config_manager(config)
    manager.print_config_comparison()
    
    paper_summary = manager.get_model_summary()
    logger.info(f"论文模式摘要: {paper_summary}")
    
    # 测试扩展模式
    config['training']['diffusion']['mode'] = 'extended'
    manager = create_config_manager(config)
    
    extended_summary = manager.get_model_summary()
    logger.info(f"扩展模式摘要: {extended_summary}")
    
    logger.info("配置管理器测试完成 ✅")


def test_unet_structure():
    """测试UNet结构"""
    logger.info("测试UNet结构...")

    config = load_config()
    manager = create_config_manager(config)

    # 测试不同的配置
    test_configs = [
        {
            'name': '论文图配置 - 5层U-Net (64→128→256→512→1024) 无注意力',
            'use_skip_connections': True,
            'encoder_channels': [64, 128, 256, 512, 1024],
            'num_layers_per_block': 2,
            'use_attention': False,  # 论文中不使用注意力机制
            'hidden_dim': 64
        },
        {
            'name': '原始配置 - 4层U-Net (128→256→512→1024) 无注意力',
            'use_skip_connections': True,
            'encoder_channels': [128, 256, 512, 1024],
            'num_layers_per_block': 2,
            'use_attention': False,  # 论文中不使用注意力机制
            'hidden_dim': 128
        },
        {
            'name': '简化模式 - 禁用skip connections',
            'use_skip_connections': False,
            'encoder_channels': [64, 128, 256, 512],
            'num_layers_per_block': 1,
            'use_attention': False,
            'hidden_dim': 64
        }
    ]
    
    for test_config in test_configs:
        logger.info(f"\n测试配置: {test_config['name']}")
        
        # 更新配置
        config['models']['diffusion'].update({
            'use_skip_connections': test_config['use_skip_connections'],
            'encoder_channels': test_config['encoder_channels'],
            'num_layers_per_block': test_config['num_layers_per_block'],
            'use_attention': test_config['use_attention'],
            'hidden_dim': test_config['hidden_dim']
        })
        
        try:
            # 创建UNet模型
            unet = UNet1D(config)
            
            # 测试前向传播
            batch_size = 4
            signal_length = 1024
            num_classes = config['dataset']['datasets']['KAT']['num_classes']
            
            # 创建测试输入
            x = torch.randn(batch_size, 1, signal_length)
            timesteps = torch.randint(0, 1000, (batch_size,))
            class_labels = torch.randint(0, num_classes, (batch_size,))
            
            # 前向传播
            with torch.no_grad():
                output = unet(x, timesteps, class_labels)
            
            logger.info(f"  输入形状: {x.shape}")
            logger.info(f"  输出形状: {output.shape}")
            logger.info(f"  模型参数数量: {sum(p.numel() for p in unet.parameters()):,}")
            logger.info(f"  skip connections: {test_config['use_skip_connections']}")
            
            # 验证输出形状
            assert output.shape == x.shape, f"输出形状不匹配: {output.shape} vs {x.shape}"
            logger.info("  ✅ 形状验证通过")
            
        except Exception as e:
            logger.error(f"  ❌ 测试失败: {e}")
            raise
    
    logger.info("UNet结构测试完成 ✅")


def test_cddpm_model():
    """测试CDDPM模型"""
    logger.info("测试CDDPM模型...")
    
    config = load_config()
    manager = create_config_manager(config)
    
    try:
        # 创建CDDPM模型
        cddpm = CDDPM(config)
        
        # 测试参数
        batch_size = 4
        signal_length = 1024
        num_classes = config['dataset']['datasets']['KAT']['num_classes']
        
        # 创建测试数据
        x_start = torch.randn(batch_size, 1, signal_length)
        class_labels = torch.randint(0, num_classes, (batch_size,))
        
        # 测试训练损失计算
        with torch.no_grad():
            loss_dict = cddpm.training_losses(x_start, class_labels)
        
        logger.info(f"  训练损失: {loss_dict['loss'].item():.6f}")
        
        # 测试生成
        with torch.no_grad():
            generated = cddpm.sample(
                num_samples=2,
                signal_length=signal_length,
                class_labels=torch.tensor([0, 1])
            )
        
        logger.info(f"  生成样本形状: {generated.shape}")
        
        # 验证Classifier-Free Guidance参数
        logger.info(f"  无条件训练概率: {cddpm.unconditional_prob}")
        logger.info(f"  引导强度: {cddpm.guidance_scale}")
        logger.info(f"  时间步数: {cddpm.timesteps}")
        
        logger.info("CDDPM模型测试完成 ✅")
        
    except Exception as e:
        logger.error(f"❌ CDDPM测试失败: {e}")
        raise


def test_paper_vs_extended_mode():
    """测试论文模式vs扩展模式的差异"""
    logger.info("测试论文模式vs扩展模式...")
    
    config = load_config()
    
    modes = ['paper', 'extended']
    results = {}
    
    for mode in modes:
        logger.info(f"\n测试 {mode} 模式:")
        
        # 设置模式
        config['training']['diffusion']['mode'] = mode
        manager = create_config_manager(config)
        
        # 获取配置摘要
        summary = manager.get_model_summary()
        results[mode] = summary
        
        # 打印关键参数
        training_params = summary['training_params']
        logger.info(f"  训练轮数: {training_params['epochs']}")
        logger.info(f"  学习率: {training_params['learning_rate']}")
        logger.info(f"  权重衰减: {training_params['weight_decay']}")
    
    # 比较差异
    logger.info("\n模式对比:")
    paper_epochs = results['paper']['training_params']['epochs']
    extended_epochs = results['extended']['training_params']['epochs']
    logger.info(f"  训练轮数差异: {extended_epochs - paper_epochs} 轮")
    
    paper_wd = results['paper']['training_params']['weight_decay']
    extended_wd = results['extended']['training_params']['weight_decay']
    logger.info(f"  权重衰减差异: {extended_wd - paper_wd}")
    
    logger.info("模式对比测试完成 ✅")


def test_paper_figure_parameters():
    """测试论文图中的具体参数"""
    logger.info("测试论文图参数实现...")

    config = load_config()

    # 设置为论文图配置（严格按照论文，无注意力机制）
    config['models']['diffusion'].update({
        'hidden_dim': 64,
        'encoder_channels': [64, 128, 256, 512, 1024],
        'num_layers_per_block': 2,
        'use_attention': False,  # 论文中不使用注意力机制
        'use_skip_connections': True,
        'resblock_groups': 8
    })

    manager = create_config_manager(config)

    try:
        # 创建UNet模型
        unet = UNet1D(config)

        # 验证网络结构
        logger.info("论文图参数验证:")
        logger.info(f"  起始通道数: 64 (论文图显示)")
        logger.info(f"  编码器通道: {config['models']['diffusion']['encoder_channels']}")
        logger.info(f"  每层ResBlock数: {config['models']['diffusion']['num_layers_per_block']}")
        logger.info(f"  使用注意力机制: {config['models']['diffusion']['use_attention']} (论文中不使用)")

        # 测试模型
        batch_size = 2
        signal_length = 1024
        num_classes = config['dataset']['datasets']['KAT']['num_classes']

        x = torch.randn(batch_size, 1, signal_length)
        timesteps = torch.randint(0, 1000, (batch_size,))
        class_labels = torch.randint(0, num_classes, (batch_size,))

        with torch.no_grad():
            output = unet(x, timesteps, class_labels)

        logger.info(f"  输入形状: {x.shape}")
        logger.info(f"  输出形状: {output.shape}")

        # 计算参数数量
        total_params = sum(p.numel() for p in unet.parameters())
        trainable_params = sum(p.numel() for p in unet.parameters() if p.requires_grad)

        logger.info(f"  总参数数: {total_params:,}")
        logger.info(f"  可训练参数数: {trainable_params:,}")

        # 验证网络层数
        logger.info(f"  下采样块数: {len(unet.down_blocks)}")
        logger.info(f"  上采样块数: {len(unet.up_blocks)}")

        assert output.shape == x.shape, f"输出形状不匹配: {output.shape} vs {x.shape}"
        logger.info("  ✅ 论文图参数验证通过")

    except Exception as e:
        logger.error(f"  ❌ 论文图参数测试失败: {e}")
        raise

    logger.info("论文图参数测试完成 ✅")


def main():
    """主测试函数"""
    logger.info("开始模型改进测试...")
    logger.info("="*60)

    try:
        # 测试配置管理器
        test_config_manager()
        print()

        # 测试论文图参数
        test_paper_figure_parameters()
        print()

        # 测试UNet结构
        test_unet_structure()
        print()

        # 测试CDDPM模型
        test_cddpm_model()
        print()

        # 测试模式差异
        test_paper_vs_extended_mode()
        print()

        logger.info("="*60)
        logger.info("🎉 所有测试通过！模型改进验证成功！")

    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        raise


if __name__ == "__main__":
    main()
