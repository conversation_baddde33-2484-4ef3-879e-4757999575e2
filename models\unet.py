"""
一维UNet模型
用于扩散模型的去噪网络
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from typing import Optional


class SinusoidalPositionEmbeddings(nn.Module):
    """正弦位置编码，用于时间步编码"""
    
    def __init__(self, dim: int):
        super().__init__()
        self.dim = dim
    
    def forward(self, time: torch.Tensor) -> torch.Tensor:
        device = time.device
        half_dim = self.dim // 2
        embeddings = math.log(10000) / (half_dim - 1)
        embeddings = torch.exp(torch.arange(half_dim, device=device) * -embeddings)
        embeddings = time[:, None] * embeddings[None, :]
        embeddings = torch.cat((embeddings.sin(), embeddings.cos()), dim=-1)
        return embeddings


class ResidualBlock1D(nn.Module):
    """一维残差块 - 根据论文实现"""

    def __init__(self, in_channels: int, out_channels: int, time_emb_dim: int,
                 kernel_size: int = 3, dropout: float = 0.1):
        super().__init__()

        self.time_mlp = nn.Linear(time_emb_dim, out_channels)

        # 第一个卷积块: GroupNorm -> Swish -> Conv -> Dropout
        self.norm1 = nn.GroupNorm(8, in_channels)
        self.conv1 = nn.Conv1d(in_channels, out_channels, kernel_size,
                              padding=kernel_size//2)
        self.dropout1 = nn.Dropout(dropout)

        # 第二个卷积块: GroupNorm -> Swish -> Conv -> Dropout
        self.norm2 = nn.GroupNorm(8, out_channels)
        self.conv2 = nn.Conv1d(out_channels, out_channels, kernel_size,
                              padding=kernel_size//2)
        self.dropout2 = nn.Dropout(dropout)

        # 残差连接
        if in_channels != out_channels:
            self.residual_conv = nn.Conv1d(in_channels, out_channels, 1)
        else:
            self.residual_conv = nn.Identity()

    def forward(self, x: torch.Tensor, time_emb: torch.Tensor) -> torch.Tensor:
        residual = self.residual_conv(x)

        # 第一个卷积块: GroupNorm -> Swish -> Conv -> Time Embedding -> Dropout
        h = self.norm1(x)
        h = F.silu(h)  # Swish activation (SiLU)
        h = self.conv1(h)

        # 添加时间嵌入
        time_emb = self.time_mlp(time_emb)
        h = h + time_emb[:, :, None]
        h = self.dropout1(h)

        # 第二个卷积块: GroupNorm -> Swish -> Conv -> Dropout
        h = self.norm2(h)
        h = F.silu(h)  # Swish activation
        h = self.conv2(h)
        h = self.dropout2(h)

        # 残差连接
        return h + residual


class AttentionBlock1D(nn.Module):
    """一维注意力块"""
    
    def __init__(self, channels: int, num_heads: int = 8):
        super().__init__()
        self.channels = channels
        self.num_heads = num_heads
        self.head_dim = channels // num_heads
        
        assert channels % num_heads == 0, "channels必须能被num_heads整除"
        
        self.norm = nn.GroupNorm(8, channels)
        self.qkv = nn.Conv1d(channels, channels * 3, 1)
        self.proj_out = nn.Conv1d(channels, channels, 1)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        B, C, L = x.shape
        residual = x
        
        x = self.norm(x)
        qkv = self.qkv(x)
        
        # 重塑为多头注意力格式
        qkv = qkv.view(B, 3, self.num_heads, self.head_dim, L)
        q, k, v = qkv.unbind(1)
        
        # 计算注意力
        scale = 1.0 / math.sqrt(self.head_dim)
        attn = torch.einsum('bhdi,bhdj->bhij', q, k) * scale
        attn = F.softmax(attn, dim=-1)
        
        # 应用注意力
        out = torch.einsum('bhij,bhdj->bhdi', attn, v)
        out = out.contiguous().view(B, C, L)
        
        out = self.proj_out(out)
        return out + residual


class DownBlock1D(nn.Module):
    """下采样块"""
    
    def __init__(self, in_channels: int, out_channels: int, time_emb_dim: int,
                 num_layers: int = 2, downsample: bool = True, 
                 use_attention: bool = False, num_heads: int = 8):
        super().__init__()
        
        self.layers = nn.ModuleList()
        for i in range(num_layers):
            in_ch = in_channels if i == 0 else out_channels
            self.layers.append(ResidualBlock1D(in_ch, out_channels, time_emb_dim))
        
        self.attention = AttentionBlock1D(out_channels, num_heads) if use_attention else None
        self.downsample = nn.Conv1d(out_channels, out_channels, 3, stride=2, padding=1) if downsample else None
    
    def forward(self, x: torch.Tensor, time_emb: torch.Tensor) -> tuple:
        skip_connections = []
        
        for layer in self.layers:
            x = layer(x, time_emb)
            skip_connections.append(x)
        
        if self.attention is not None:
            x = self.attention(x)
            skip_connections[-1] = x
        
        if self.downsample is not None:
            x = self.downsample(x)
        
        return x, skip_connections


class UpBlock1D(nn.Module):
    """上采样块"""
    
    def __init__(self, in_channels: int, out_channels: int, time_emb_dim: int,
                 num_layers: int = 2, upsample: bool = True,
                 use_attention: bool = False, num_heads: int = 8):
        super().__init__()
        
        self.layers = nn.ModuleList()
        for i in range(num_layers):
            if i == 0:
                # 第一层需要考虑skip connection的通道数
                in_ch = in_channels + out_channels
            else:
                in_ch = out_channels
            self.layers.append(ResidualBlock1D(in_ch, out_channels, time_emb_dim))
        
        self.attention = AttentionBlock1D(out_channels, num_heads) if use_attention else None
        self.upsample = nn.ConvTranspose1d(out_channels, out_channels, 4, stride=2, padding=1) if upsample else None
    
    def forward(self, x: torch.Tensor, skip_connections: list, time_emb: torch.Tensor) -> torch.Tensor:
        for i, layer in enumerate(self.layers):
            if i == 0 and skip_connections:
                # 连接skip connection
                skip = skip_connections.pop()
                # 确保skip connection的尺寸匹配
                if skip.shape[-1] != x.shape[-1]:
                    # 如果长度不匹配，进行插值调整
                    skip = torch.nn.functional.interpolate(skip, size=x.shape[-1], mode='linear', align_corners=False)
                x = torch.cat([x, skip], dim=1)
            x = layer(x, time_emb)

        if self.attention is not None:
            x = self.attention(x)

        if self.upsample is not None:
            x = self.upsample(x)

        return x


class UNet1D(nn.Module):
    """一维UNet模型，用于扩散模型的去噪网络"""
    
    def __init__(self, config: dict, in_channels: int = 1, out_channels: int = 1):
        super().__init__()
        
        # 从配置中获取参数
        model_config = config['models']['diffusion']
        hidden_dim = model_config.get('hidden_dim', 128)
        num_layers = model_config.get('num_layers', 4)
        num_heads = model_config.get('num_heads', 8)
        dropout = model_config.get('dropout', 0.1)

        # 新增配置参数
        self.use_skip_connections = model_config.get('use_skip_connections', True)
        encoder_channels = model_config.get('encoder_channels', [128, 256, 512, 1024])
        num_layers_per_block = model_config.get('num_layers_per_block', 2)
        attention_layers = model_config.get('attention_layers', [1, 2])
        resblock_kernel_size = model_config.get('resblock_kernel_size', 3)
        resblock_groups = model_config.get('resblock_groups', 8)
        
        # 时间嵌入维度
        time_emb_dim = hidden_dim * 4
        
        # 时间嵌入
        self.time_embedding = nn.Sequential(
            SinusoidalPositionEmbeddings(hidden_dim),
            nn.Linear(hidden_dim, time_emb_dim),
            nn.ReLU(),
            nn.Linear(time_emb_dim, time_emb_dim)
        )
        
        # 类别嵌入（用于条件生成）
        # +1 是为了支持无条件生成（使用-1索引映射到最后一个嵌入）
        num_classes = config['dataset']['datasets'][config['dataset']['name']]['num_classes']
        self.class_embedding = nn.Embedding(num_classes + 1, time_emb_dim)
        self.num_classes = num_classes
        
        # 输入投影
        self.input_conv = nn.Conv1d(in_channels, hidden_dim, 3, padding=1)
        
        # 编码器（下采样）- 使用配置化的通道数
        self.down_blocks = nn.ModuleList()
        # 确保第一个通道是hidden_dim
        if encoder_channels[0] != hidden_dim:
            channels = [hidden_dim] + encoder_channels[1:]
        else:
            channels = encoder_channels

        for i in range(len(channels)-1):
            in_ch = channels[i]
            out_ch = channels[i+1]
            use_attention = i in attention_layers  # 根据配置决定是否使用注意力

            self.down_blocks.append(
                DownBlock1D(in_ch, out_ch, time_emb_dim,
                           num_layers=num_layers_per_block, downsample=True,
                           use_attention=use_attention, num_heads=num_heads)
            )
        
        # 中间块
        self.mid_block1 = ResidualBlock1D(channels[-1], channels[-1], time_emb_dim)
        self.mid_attention = AttentionBlock1D(channels[-1], num_heads)
        self.mid_block2 = ResidualBlock1D(channels[-1], channels[-1], time_emb_dim)
        
        # 解码器（上采样）- 支持可配置的skip connections
        self.up_blocks = nn.ModuleList()
        channels_reversed = list(reversed(channels))

        for i in range(len(channels_reversed)-1):
            in_ch = channels_reversed[i]
            out_ch = channels_reversed[i+1]
            # 解码器中的注意力层配置（与编码器对称）
            decoder_attention_layer = len(channels_reversed) - 2 - i
            use_attention = decoder_attention_layer in attention_layers

            if self.use_skip_connections:
                # 使用完整的UpBlock1D，包含skip connections
                self.up_blocks.append(
                    UpBlock1D(in_ch, out_ch, time_emb_dim,
                             num_layers=num_layers_per_block, upsample=True,
                             use_attention=use_attention, num_heads=num_heads)
                )
            else:
                # 简化版本，不使用skip connections（向后兼容）
                self.up_blocks.append(
                    nn.Sequential(
                        ResidualBlock1D(in_ch, out_ch, time_emb_dim),
                        ResidualBlock1D(out_ch, out_ch, time_emb_dim),
                        nn.ConvTranspose1d(out_ch, out_ch, 4, stride=2, padding=1)
                    )
                )
        
        # 输出投影
        self.output_conv = nn.Sequential(
            nn.GroupNorm(8, hidden_dim),
            nn.ReLU(),
            nn.Conv1d(hidden_dim, out_channels, 3, padding=1)
        )
    
    def forward(self, x: torch.Tensor, timesteps: torch.Tensor, 
                class_labels: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 输入信号，形状为 (batch_size, channels, length)
            timesteps: 时间步，形状为 (batch_size,)
            class_labels: 类别标签，形状为 (batch_size,)，用于条件生成
            
        Returns:
            去噪后的信号，形状与输入相同
        """
        # 时间嵌入
        time_emb = self.time_embedding(timesteps)
        
        # 类别嵌入（支持条件和无条件生成）
        if class_labels is not None:
            # 将-1映射到最后一个嵌入索引（无条件）
            processed_labels = torch.where(class_labels == -1,
                                          torch.tensor(self.num_classes, device=class_labels.device),
                                          class_labels)
            class_emb = self.class_embedding(processed_labels)
            time_emb = time_emb + class_emb
        
        # 输入投影
        x = self.input_conv(x)

        # 编码器 - 收集skip connections
        skip_connections = []
        for down_block in self.down_blocks:
            x, skips = down_block(x, time_emb)
            skip_connections.extend(skips)

        # 中间块
        x = self.mid_block1(x, time_emb)
        x = self.mid_attention(x)
        x = self.mid_block2(x, time_emb)

        # 解码器 - 根据配置决定是否使用skip connections
        if self.use_skip_connections:
            # 使用skip connections的完整U-Net
            # 反转skip_connections以匹配解码器顺序
            skip_connections = list(reversed(skip_connections))

            for up_block in self.up_blocks:
                if isinstance(up_block, UpBlock1D):
                    # 为每个上采样块分配对应的skip connections
                    # 每个下采样块产生num_layers_per_block个skip connections
                    block_skips = []
                    if skip_connections:
                        # 取出这个块需要的skip connections
                        num_layers_per_block = getattr(self, 'num_layers_per_block', 2)
                        for _ in range(num_layers_per_block):
                            if skip_connections:
                                block_skips.append(skip_connections.pop(0))

                    x = up_block(x, block_skips, time_emb)
                else:
                    # 简化版本的处理
                    for layer in up_block:
                        if isinstance(layer, ResidualBlock1D):
                            x = layer(x, time_emb)
                        else:
                            x = layer(x)
        else:
            # 简化版本，不使用skip connections
            for up_block in self.up_blocks:
                for layer in up_block:
                    if isinstance(layer, ResidualBlock1D):
                        x = layer(x, time_emb)
                    else:
                        x = layer(x)
        
        # 输出投影
        x = self.output_conv(x)
        
        return x
